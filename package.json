{"name": "browseforme", "displayName": "browseforme", "version": "0.0.1", "description": "帮我刷帖", "author": "kongkongye", "scripts": {"dev": "rm -rf build && plasmo dev", "build": "plasmo build", "package": "plasmo package", "test": "./node_modules/.bin/jest", "test:watch": "./node_modules/.bin/jest --watch", "test:coverage": "./node_modules/.bin/jest --coverage", "test:streaming": "./node_modules/.bin/jest tests/streaming.test.ts", "test:openai": "./node_modules/.bin/jest tests/openai.test.ts", "test:basic": "./node_modules/.bin/jest tests/basic.test.ts"}, "dependencies": {"@plasmohq/messaging": "^0.7.1", "@plasmohq/redux-persist": "^6.1.0", "@plasmohq/storage": "^1.15.0", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@reduxjs/toolkit": "^2.6.1", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "framer-motion": "^12.5.0", "jsonrepair": "^3.13.0", "next-themes": "^0.4.6", "openai": "^5.10.2", "plasmo": "0.90.3", "react": "18.2.0", "react-d3-cloud": "^1.0.6", "react-dom": "18.2.0", "react-redux": "^9.2.0", "redux-persist-webextension-storage": "^1.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@tailwindcss/typography": "^0.5.16", "@types/chrome": "0.0.258", "@types/d3-scale": "^4.0.9", "@types/d3-scale-chromatic": "^3.1.0", "@types/jest": "^30.0.0", "@types/node": "20.11.5", "@types/node-fetch": "^2.6.12", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "lucide-react": "^0.476.0", "postcss": "^8.5.3", "prettier": "3.2.4", "tailwind-merge": "^3.0.2", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^3.3.0", "ts-jest": "^29.4.0", "typescript": "5.3.3"}, "manifest": {"host_permissions": ["https://*/*", "http://*/*"], "permissions": ["activeTab", "tabs", "storage", "scripting", "sidePanel"], "web_accessible_resources": [{"resources": ["*.css", "assets/*.md"], "matches": ["<all_urls>"]}], "options_page": "options.html"}}