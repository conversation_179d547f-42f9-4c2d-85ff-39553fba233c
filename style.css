@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
} 

/* Shadow DOM 容器样式 */
#plasmo-shadow-container {
  z-index: 9999 !important;
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
}

#plasmo-inline {
  background: transparent !important;
}

/* 确保所有内容在Shadow DOM中正确显示 */
:host {
  all: initial;
}

/* 词云 SVG 文本元素样式 - 确保鼠标指针样式正确显示 */
svg text {
  cursor: pointer !important;
}

/* 更具体的选择器，确保在 Shadow DOM 中生效 */
:host svg text,
:host() svg text {
  cursor: pointer !important;
}

/* 针对 react-d3-cloud 生成的 SVG 文本元素 */
.browseforme-wordcloud-panel svg text,
.wordcloud-container svg text {
  cursor: pointer !important;
  transition: opacity 0.2s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 悬停时的视觉反馈 */
.browseforme-wordcloud-panel svg text:hover,
.wordcloud-container svg text:hover {
  opacity: 0.8;
}

/* 全局 SVG 文本样式，确保在任何情况下都生效 */
* svg text {
  cursor: pointer !important;
}

/* 针对 Shadow DOM 的特殊处理 */
:host * svg text,
:host(*) svg text {
  cursor: pointer !important;
}

/* 使用属性选择器确保样式应用 */
text[style*="cursor"] {
  cursor: pointer !important;
}