import React, { useCallback, useMemo, useEffect, useRef } from 'react'
import WordCloud from 'react-d3-cloud'
import { scaleOrdinal } from 'd3-scale'
import { schemeCategory10 } from 'd3-scale-chromatic'
import { setupWordCloudCursorFix } from '../../lib/wordcloudCursorFix'

interface PostInfo {
  id: number
  title: string
  link?: string
  author?: string
  time?: string
  xpath?: string
}

interface WordCloudItem {
  text: string
  size: number
  relatedPosts?: PostInfo[]
}

interface WordCloudComponentProps {
  words: WordCloudItem[]
  width?: number
  height?: number
  minFontSize?: number
  maxFontSize?: number
  onWordHover?: (word: string | null, posts?: PostInfo[], event?: MouseEvent) => void
  onWordClick?: (word: string, posts?: PostInfo[]) => void
}

const WordCloudComponent: React.FC<WordCloudComponentProps> = ({
  words,
  width = 400,
  height = 300,
  minFontSize = 12,
  maxFontSize = 48,
  onWordHover,
  onWordClick
}) => {
  // 创建颜色比例尺
  const colorScale = useMemo(() => scaleOrdinal(schemeCategory10), [])

  // 转换数据格式为react-d3-cloud需要的格式
  const cloudData = useMemo(() => {
    return words.map(word => ({
      text: word.text,
      value: word.size,
      relatedPosts: word.relatedPosts
    }))
  }, [words])

  // 计算字体大小的函数
  const fontSize = useCallback((word: any) => {
    if (!words.length) return minFontSize

    const maxSize = Math.max(...words.map(w => w.size))
    const minSize = Math.min(...words.map(w => w.size))

    if (maxSize === minSize) return minFontSize

    const ratio = (word.value - minSize) / (maxSize - minSize)
    return Math.round(minFontSize + ratio * (maxFontSize - minFontSize))
  }, [words, minFontSize, maxFontSize])

  // 旋转角度函数
  const rotate = useCallback((word: any) => {
    // 使用固定的随机种子避免每次重新生成随机旋转
    return ((word.text.charCodeAt(0) + word.value) % 13 - 6) * 5 // -30到30度的固定旋转
  }, [])

  // 颜色填充函数
  const fill = useCallback((_d: any, i: number) => {
    return colorScale(i.toString())
  }, [colorScale])

  // 处理词云点击事件
  const handleWordClick = useCallback((event: any, word: any) => {
    // 阻止事件冒泡，避免触发面板的点击处理器
    event.preventDefault?.()
    event.stopPropagation?.()

    // 查找对应的词云项
    const wordItem = words.find(w => w.text === word.text)
    if (wordItem && onWordClick) {
      onWordClick(wordItem.text, wordItem.relatedPosts)
    }
  }, [words, onWordClick])

  // 处理词云鼠标悬停事件
  const handleWordMouseOver = useCallback((event: any, word: any) => {
    // 查找对应的词云项
    const wordItem = words.find(w => w.text === word.text)
    if (wordItem && onWordHover) {
      onWordHover(wordItem.text, wordItem.relatedPosts, event.nativeEvent || event)
    }
  }, [words, onWordHover])

  // 处理词云鼠标离开事件
  const handleWordMouseOut = useCallback((_event: any, _word: any) => {
    if (onWordHover) {
      onWordHover(null)
    }
  }, [onWordHover])

  // 使用 ref 来直接操作 SVG 元素
  const wordCloudRef = useRef<HTMLDivElement>(null)

  // 使用我们的工具函数来处理鼠标指针样式
  useEffect(() => {
    if (!wordCloudRef.current) return

    // 设置词云鼠标指针修复
    const cleanup = setupWordCloudCursorFix(wordCloudRef.current)

    return cleanup
  }, [cloudData]) // 当词云数据变化时重新设置

  return (
    <div
      ref={wordCloudRef}
      className="bg-white border border-gray-200 rounded-lg overflow-hidden wordcloud-container"
      style={{
        // 确保容器内的 SVG 文本元素有正确的样式
        '--wordcloud-cursor': 'pointer'
      } as React.CSSProperties}
    >
      <WordCloud
        data={cloudData}
        width={width}
        height={height}
        font="sans-serif"
        fontWeight="bold"
        fontSize={fontSize}
        spiral="archimedean"
        rotate={rotate}
        padding={5}
        random={() => 0.5} // 使用固定值确保布局稳定
        fill={fill}
        onWordClick={handleWordClick}
        onWordMouseOver={handleWordMouseOver}
        onWordMouseOut={handleWordMouseOut}
      />
    </div>
  )
}

export default WordCloudComponent