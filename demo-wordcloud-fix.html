<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词云鼠标指针修复演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            max-width: 800px;
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .demo-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .solution-steps {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
        }
        
        .solution-steps h3 {
            margin-top: 0;
            color: #155724;
        }
        
        .solution-steps ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .solution-steps li {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }
        
        .warning-box h4 {
            margin-top: 0;
            color: #856404;
        }
        
        .test-section {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            background: #fafafa;
        }
        
        .wordcloud-container svg text {
            cursor: pointer !important;
            transition: opacity 0.2s ease-in-out;
            user-select: none;
        }
        
        .wordcloud-container svg text:hover {
            opacity: 0.8;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">react-d3-cloud 词云鼠标指针样式修复方案</h1>
        
        <div class="demo-description">
            <p>这个演示展示了如何解决 react-d3-cloud 在浏览器扩展的 Shadow DOM 环境中鼠标指针样式不生效的问题。</p>
        </div>
        
        <div class="warning-box">
            <h4>问题描述</h4>
            <p>在使用 react-d3-cloud 创建词云组件时，当鼠标悬停在词云的文字上时，鼠标指针没有变成手指图案（cursor: pointer），这让用户无法直观地知道这些文字是可以点击的。</p>
            <p>这个问题在浏览器扩展的 Shadow DOM 环境中尤其明显，因为样式隔离导致 CSS 无法正确穿透到 SVG 元素。</p>
        </div>
        
        <div class="solution-steps">
            <h3>解决方案步骤</h3>
            <ol>
                <li><strong>CSS 样式增强</strong>：在 style.css 中添加多层级的 CSS 选择器</li>
                <li><strong>JavaScript 动态设置</strong>：使用 JavaScript 直接设置 SVG 文本元素的样式</li>
                <li><strong>MutationObserver 监听</strong>：监听 DOM 变化，确保新生成的元素也有正确的样式</li>
                <li><strong>工具函数封装</strong>：创建可复用的工具函数来处理样式问题</li>
            </ol>
        </div>
        
        <h3>1. CSS 样式解决方案</h3>
        <div class="code-block">
/* 词云 SVG 文本元素样式 - 确保鼠标指针样式正确显示 */
.browseforme-wordcloud-panel svg text,
.wordcloud-container svg text {
  cursor: pointer !important;
  transition: opacity 0.2s ease-in-out;
  user-select: none;
}

/* 悬停时的视觉反馈 */
.wordcloud-container svg text:hover {
  opacity: 0.8;
}

/* 针对 Shadow DOM 的特殊处理 */
:host svg text,
:host() svg text {
  cursor: pointer !important;
}

/* 全局 SVG 文本样式，确保在任何情况下都生效 */
* svg text {
  cursor: pointer !important;
}
        </div>
        
        <h3>2. JavaScript 动态设置方案</h3>
        <div class="code-block">
// 应用 cursor 样式到 SVG 文本元素
function applyCursorStylesToWordCloud(container) {
  const svgTextElements = container.querySelectorAll('svg text');
  
  svgTextElements.forEach((textElement) => {
    textElement.style.cursor = 'pointer';
    textElement.style.userSelect = 'none';
    textElement.classList.add('wordcloud-text-clickable');
    
    textElement.addEventListener('mouseenter', () => {
      textElement.style.cursor = 'pointer';
    });
  });
}
        </div>
        
        <h3>3. React 组件集成</h3>
        <div class="code-block">
// 在 WordCloud 组件中使用
useEffect(() => {
  if (!wordCloudRef.current) return;
  
  const cleanup = setupWordCloudCursorFix(wordCloudRef.current);
  return cleanup;
}, [cloudData]);
        </div>
        
        <h3>4. 测试效果</h3>
        <div class="test-section">
            <p>将鼠标悬停在下面的文字上，观察鼠标指针是否变成手指图案：</p>
            <div class="wordcloud-container">
                <svg width="400" height="150">
                    <text x="50" y="40" font-size="24" fill="#e74c3c" font-weight="bold">React</text>
                    <text x="150" y="40" font-size="20" fill="#3498db">TypeScript</text>
                    <text x="280" y="40" font-size="18" fill="#2ecc71">Tailwind</text>
                    <text x="80" y="80" font-size="16" fill="#f39c12">Plasmo</text>
                    <text x="200" y="80" font-size="22" fill="#9b59b6">词云</text>
                    <text x="50" y="120" font-size="14" fill="#34495e">浏览器扩展</text>
                    <text x="200" y="120" font-size="16" fill="#e67e22">Shadow DOM</text>
                </svg>
            </div>
            <p id="cursor-status">将鼠标悬停在文字上查看状态...</p>
        </div>
        
        <div class="solution-steps">
            <h3>关键技术点</h3>
            <ol>
                <li><strong>多层级 CSS 选择器</strong>：确保在不同环境下都能命中目标元素</li>
                <li><strong>!important 声明</strong>：覆盖可能存在的其他样式规则</li>
                <li><strong>JavaScript 直接设置</strong>：绕过 CSS 样式隔离问题</li>
                <li><strong>事件监听器</strong>：确保样式在动态变化时保持正确</li>
                <li><strong>MutationObserver</strong>：监听 DOM 变化，自动应用样式</li>
            </ol>
        </div>
    </div>

    <script>
        // 应用我们的解决方案
        function setupWordCloudDemo() {
            const container = document.querySelector('.wordcloud-container');
            const statusElement = document.getElementById('cursor-status');
            
            if (!container) return;
            
            const textElements = container.querySelectorAll('svg text');
            
            textElements.forEach((text, index) => {
                // 应用样式
                text.style.cursor = 'pointer';
                text.style.userSelect = 'none';
                
                // 添加点击事件
                text.addEventListener('click', function() {
                    alert(`点击了文字: ${this.textContent}`);
                });
                
                // 添加悬停事件
                text.addEventListener('mouseenter', function() {
                    const computedStyle = window.getComputedStyle(this);
                    const cursor = computedStyle.cursor;
                    
                    if (cursor === 'pointer') {
                        statusElement.innerHTML = `✅ 鼠标指针样式正确 - "${this.textContent}" (cursor: ${cursor})`;
                        statusElement.className = 'status-success';
                    } else {
                        statusElement.innerHTML = `❌ 鼠标指针样式错误 - "${this.textContent}" (cursor: ${cursor})`;
                        statusElement.className = 'status-warning';
                    }
                });
                
                text.addEventListener('mouseleave', function() {
                    statusElement.innerHTML = '将鼠标悬停在文字上查看状态...';
                    statusElement.className = '';
                });
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', setupWordCloudDemo);
    </script>
</body>
</html>
