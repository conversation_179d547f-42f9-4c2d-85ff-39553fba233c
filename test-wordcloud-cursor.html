<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词云鼠标指针测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        
        /* 模拟 Shadow DOM 样式隔离 */
        .shadow-dom-simulation {
            border: 2px dashed #ccc;
            padding: 15px;
            background: #fafafa;
        }
        
        /* 测试不同的 cursor 样式方案 */
        .test-1 svg text {
            cursor: pointer;
        }
        
        .test-2 svg text {
            cursor: pointer !important;
        }
        
        .test-3 * svg text {
            cursor: pointer !important;
        }
        
        .test-4 text {
            cursor: pointer !important;
        }
        
        /* 模拟我们的解决方案 */
        .wordcloud-container svg text {
            cursor: pointer !important;
            transition: opacity 0.2s ease-in-out;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        .wordcloud-container svg text:hover {
            opacity: 0.8;
        }
        
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>react-d3-cloud 词云鼠标指针样式测试</h1>
    
    <div class="test-container">
        <div class="test-title">测试 1: 基础 CSS 选择器</div>
        <div class="test-description">使用 <code>svg text { cursor: pointer; }</code></div>
        <div class="test-1">
            <svg width="300" height="100">
                <text x="50" y="30" font-size="16" fill="#333">测试文字 1</text>
                <text x="150" y="30" font-size="20" fill="#666">测试文字 2</text>
                <text x="50" y="60" font-size="14" fill="#999">测试文字 3</text>
            </svg>
        </div>
        <div class="status" id="status-1">将鼠标悬停在文字上查看指针样式</div>
    </div>
    
    <div class="test-container">
        <div class="test-title">测试 2: 使用 !important</div>
        <div class="test-description">使用 <code>svg text { cursor: pointer !important; }</code></div>
        <div class="test-2">
            <svg width="300" height="100">
                <text x="50" y="30" font-size="16" fill="#333">测试文字 1</text>
                <text x="150" y="30" font-size="20" fill="#666">测试文字 2</text>
                <text x="50" y="60" font-size="14" fill="#999">测试文字 3</text>
            </svg>
        </div>
        <div class="status" id="status-2">将鼠标悬停在文字上查看指针样式</div>
    </div>
    
    <div class="test-container">
        <div class="test-title">测试 3: 通配符选择器</div>
        <div class="test-description">使用 <code>* svg text { cursor: pointer !important; }</code></div>
        <div class="test-3">
            <svg width="300" height="100">
                <text x="50" y="30" font-size="16" fill="#333">测试文字 1</text>
                <text x="150" y="30" font-size="20" fill="#666">测试文字 2</text>
                <text x="50" y="60" font-size="14" fill="#999">测试文字 3</text>
            </svg>
        </div>
        <div class="status" id="status-3">将鼠标悬停在文字上查看指针样式</div>
    </div>
    
    <div class="test-container">
        <div class="test-title">测试 4: 直接选择 text 元素</div>
        <div class="test-description">使用 <code>text { cursor: pointer !important; }</code></div>
        <div class="test-4">
            <svg width="300" height="100">
                <text x="50" y="30" font-size="16" fill="#333">测试文字 1</text>
                <text x="150" y="30" font-size="20" fill="#666">测试文字 2</text>
                <text x="50" y="60" font-size="14" fill="#999">测试文字 3</text>
            </svg>
        </div>
        <div class="status" id="status-4">将鼠标悬停在文字上查看指针样式</div>
    </div>
    
    <div class="test-container">
        <div class="test-title">测试 5: 我们的解决方案</div>
        <div class="test-description">使用 <code>.wordcloud-container svg text</code> 选择器 + JavaScript 动态设置</div>
        <div class="wordcloud-container">
            <svg width="300" height="100">
                <text x="50" y="30" font-size="16" fill="#333">测试文字 1</text>
                <text x="150" y="30" font-size="20" fill="#666">测试文字 2</text>
                <text x="50" y="60" font-size="14" fill="#999">测试文字 3</text>
            </svg>
        </div>
        <div class="status success" id="status-5">这是我们推荐的解决方案</div>
    </div>
    
    <div class="test-container">
        <div class="test-title">测试 6: Shadow DOM 模拟</div>
        <div class="test-description">模拟 Shadow DOM 环境下的样式隔离</div>
        <div class="shadow-dom-simulation">
            <div class="wordcloud-container">
                <svg width="300" height="100">
                    <text x="50" y="30" font-size="16" fill="#333">Shadow DOM 文字 1</text>
                    <text x="150" y="30" font-size="20" fill="#666">Shadow DOM 文字 2</text>
                    <text x="50" y="60" font-size="14" fill="#999">Shadow DOM 文字 3</text>
                </svg>
            </div>
        </div>
        <div class="status" id="status-6">在模拟的 Shadow DOM 环境中测试</div>
    </div>

    <script>
        // 动态设置样式的 JavaScript 解决方案
        function applyCursorStyles() {
            const containers = document.querySelectorAll('.wordcloud-container');
            containers.forEach(container => {
                const textElements = container.querySelectorAll('svg text');
                textElements.forEach(text => {
                    text.style.cursor = 'pointer';
                    text.style.userSelect = 'none';
                    
                    // 添加点击事件测试
                    text.addEventListener('click', function() {
                        alert('点击了文字: ' + this.textContent);
                    });
                    
                    // 添加悬停事件测试
                    text.addEventListener('mouseenter', function() {
                        this.style.cursor = 'pointer';
                        console.log('鼠标悬停在文字上:', this.textContent);
                    });
                });
            });
        }
        
        // 页面加载完成后应用样式
        document.addEventListener('DOMContentLoaded', applyCursorStyles);
        
        // 检测鼠标指针样式的函数
        function checkCursorStyle(element) {
            const computedStyle = window.getComputedStyle(element);
            return computedStyle.cursor;
        }
        
        // 为每个测试添加鼠标事件监听
        document.querySelectorAll('svg text').forEach((text, index) => {
            text.addEventListener('mouseenter', function() {
                const cursor = checkCursorStyle(this);
                const testNumber = Math.floor(index / 3) + 1;
                const statusElement = document.getElementById(`status-${testNumber}`);
                
                if (cursor === 'pointer') {
                    statusElement.textContent = '✅ 鼠标指针样式正确 (cursor: pointer)';
                    statusElement.className = 'status success';
                } else {
                    statusElement.textContent = `❌ 鼠标指针样式错误 (cursor: ${cursor})`;
                    statusElement.className = 'status warning';
                }
            });
            
            text.addEventListener('mouseleave', function() {
                const testNumber = Math.floor(index / 3) + 1;
                const statusElement = document.getElementById(`status-${testNumber}`);
                statusElement.textContent = '将鼠标悬停在文字上查看指针样式';
                statusElement.className = 'status';
            });
        });
    </script>
</body>
</html>
