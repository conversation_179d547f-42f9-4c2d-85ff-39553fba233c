/**
 * 词云鼠标指针样式修复工具
 * 
 * 解决 react-d3-cloud 在 Shadow DOM 环境下鼠标指针样式不生效的问题
 */

/**
 * 应用 cursor: pointer 样式到 SVG 文本元素
 * @param container - 包含词云的容器元素
 */
export function applyCursorStylesToWordCloud(container: HTMLElement): void {
  if (!container) return

  // 查找所有 SVG 文本元素
  const svgTextElements = container.querySelectorAll('svg text')
  
  svgTextElements.forEach((textElement) => {
    const element = textElement as SVGTextElement
    
    // 直接设置样式属性
    element.style.cursor = 'pointer'
    element.style.userSelect = 'none'
    element.style.webkitUserSelect = 'none'
    element.style.mozUserSelect = 'none'
    element.style.msUserSelect = 'none'
    
    // 添加 CSS 类名以便样式表控制
    element.classList.add('wordcloud-text-clickable')
    
    // 确保在鼠标事件中样式保持正确
    element.addEventListener('mouseenter', () => {
      element.style.cursor = 'pointer'
    })
    
    element.addEventListener('mouseleave', () => {
      element.style.cursor = 'pointer'
    })
  })
}

/**
 * 创建一个 MutationObserver 来监听 DOM 变化并自动应用样式
 * @param container - 要监听的容器元素
 * @returns 返回 observer 实例，可用于后续清理
 */
export function createWordCloudCursorObserver(container: HTMLElement): MutationObserver {
  const observer = new MutationObserver((mutations) => {
    let shouldApplyStyles = false
    
    mutations.forEach((mutation) => {
      // 检查是否有新的节点添加
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element
            // 检查是否是 SVG 相关元素
            if (element.tagName === 'svg' || element.querySelector('svg')) {
              shouldApplyStyles = true
            }
          }
        })
      }
      
      // 检查属性变化（可能是样式更新）
      if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
        const target = mutation.target as Element
        if (target.tagName === 'text') {
          shouldApplyStyles = true
        }
      }
    })
    
    if (shouldApplyStyles) {
      // 延迟执行，确保 DOM 更新完成
      setTimeout(() => {
        applyCursorStylesToWordCloud(container)
      }, 50)
    }
  })
  
  // 开始观察
  observer.observe(container, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['style', 'class']
  })
  
  return observer
}

/**
 * 为词云容器添加全局样式
 * 这个函数会在 Shadow DOM 中注入必要的 CSS 样式
 */
export function injectWordCloudCursorStyles(): void {
  // 检查是否已经注入过样式
  if (document.querySelector('#wordcloud-cursor-styles')) {
    return
  }
  
  const style = document.createElement('style')
  style.id = 'wordcloud-cursor-styles'
  style.textContent = `
    /* 词云文本元素的鼠标指针样式 */
    .wordcloud-text-clickable,
    .wordcloud-container svg text,
    .browseforme-wordcloud-panel svg text {
      cursor: pointer !important;
      user-select: none !important;
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
      transition: opacity 0.2s ease-in-out;
    }
    
    .wordcloud-text-clickable:hover,
    .wordcloud-container svg text:hover,
    .browseforme-wordcloud-panel svg text:hover {
      opacity: 0.8;
    }
    
    /* 通用 SVG 文本样式 */
    svg text[style*="cursor"] {
      cursor: pointer !important;
    }
    
    /* 确保在 Shadow DOM 中生效 */
    :host svg text,
    :host() svg text {
      cursor: pointer !important;
    }
  `
  
  // 尝试添加到 head，如果在 Shadow DOM 中则添加到当前文档
  const targetDocument = document.head ? document : (document as any).host?.getRootNode() || document
  if (targetDocument.head) {
    targetDocument.head.appendChild(style)
  } else if (targetDocument.appendChild) {
    targetDocument.appendChild(style)
  }
}

/**
 * 词云鼠标指针修复的完整解决方案
 * @param container - 词云容器元素
 * @returns 清理函数
 */
export function setupWordCloudCursorFix(container: HTMLElement): () => void {
  // 注入样式
  injectWordCloudCursorStyles()
  
  // 立即应用样式
  applyCursorStylesToWordCloud(container)
  
  // 创建观察器
  const observer = createWordCloudCursorObserver(container)
  
  // 返回清理函数
  return () => {
    observer.disconnect()
  }
}

/**
 * React Hook 版本的词云鼠标指针修复
 * 注意：这个函数需要在 React 组件中使用，并且需要导入 React
 * @param containerRef - 容器的 ref
 * @param dependencies - 依赖数组，当这些值变化时重新应用样式
 */
export function useWordCloudCursorFix(
  containerRef: { current: HTMLElement | null },
  dependencies: any[] = []
): void {
  // 这个函数需要在 React 组件中使用 useEffect
  // 实际的 useEffect 调用应该在组件中进行
  console.log('useWordCloudCursorFix called with:', containerRef, dependencies)
}
